---
description: 
globs: 
alwaysApply: true
---

# TDD 

- Before writing implementation code, you MUST write a test that describes the expected behavior and you MUST make sure it DOES NOT pass
- After seeing that the new test fails, write minimum implementation to make the test pass

# Using server

- Do not try to run HTTP development server. I am testing UI manually and I give you information about manual test results.