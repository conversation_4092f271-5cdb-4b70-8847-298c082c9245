---
description: When you edit code
globs: *.*
alwaysApply: true
---
# General rules for editing code

- DO NOT reorder statements like `import` or `require` or elements in a list etc. UNLESS you are explicitly asked to do it
- You MUST NOT write code comments that explain what the code does UNLESS you are explicitly asked to do so
- When adding new tests, DO NOT change existing tests and DO NOT remove any of the existing tests UNLESS they are outdated and no longer needed due to the changes in implementation
