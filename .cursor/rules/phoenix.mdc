---
description:
globs:
alwaysApply: true
---
# Controllers and LiveView Components

- Do not use Repo and/or Ecto queries directly - always encapsulate domain-specific database logic in context modules and use them in controllers and LiveView components. Example:
  ```elixir
  # BAD
  case Repo.get_by(Repository, full_name: full_name, template: true) do
    nil -> {:error, :not_found}
    repo -> {:ok, repo}
  end

  # GOOD
  Repositories.get_by_full_name(template: true)
  ```

- Do not write long and complex heex templates - complex elements should be handled by components

# Testing

- DO NOT use complex CSS selectors in tests that use class names or complex expressions
- Use data-* attributes to easily identify certain elements in the UI
- If data-* attributes are missing in the markup - suggest that we should add them

# Phoenix LiveView
- Use :live_view components only if state is required
- Use :live_component components only if state is required
- Do not introduce more than 6 assigns in a component. If more assigns are needed, introduce a custom struct to encapsulate them logically.
- Do not write components that are longer than around 500 lines of code. Split them into smaller components if they are too long.
- Do not write complex render functions. HEEX must be simple and more complex logic should be encapsulated by either live components or regular components.

# UI and Markup

- Use `phx-disable-with` feature for showing loading indicators after clicking on a button that triggers a request

