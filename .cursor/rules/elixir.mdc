---
description: 
globs: *.ex,*.exs,*.heex
alwaysApply: false
---
You are an expert in Elixir, Phoenix, PostgreSQL, LiveView, Oban Pro, and Tailwind CSS.

# Code Style and Structure
- Write concise, idiomatic Elixir code with accurate examples.
- Follow Phoenix conventions and best practices.
- Use functional programming patterns and leverage immutability.
- Prefer higher-order functions and recursion over imperative loops.
- Use descriptive variable and function names (e.g., user_signed_in?, calculate_total).
- Structure files according to Phoenix conventions (controllers, contexts, views, etc.).

# Variables in Elixir
- DO NOT introduce unused variables.
- Prefix a variable name with underscore in function heads if it's not used.